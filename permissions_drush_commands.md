# 📋 Commandes Drush - Configuration des Permissions par Rôle

## 🎯 Rôle : `direction_des_systemes_d_information`

### Permissions de base (backoffice)
```bash
./vendor/bin/drush role:perm:add direction_des_systemes_d_information "access administration pages"
./vendor/bin/drush role:perm:add direction_des_systemes_d_information "access toolbar"
./vendor/bin/drush role:perm:add direction_des_systemes_d_information "view the administration theme"
./vendor/bin/drush role:perm:add direction_des_systemes_d_information "access content overview"
./vendor/bin/drush role:perm:add direction_des_systemes_d_information "view own unpublished content"
./vendor/bin/drush role:perm:add direction_des_systemes_d_information "view all revisions"
```

### Permissions pour E-Services
```bash
./vendor/bin/drush role:perm:add direction_des_systemes_d_information "create e_service content"
./vendor/bin/drush role:perm:add direction_des_systemes_d_information "edit any e_service content"
./vendor/bin/drush role:perm:add direction_des_systemes_d_information "edit own e_service content"
./vendor/bin/drush role:perm:add direction_des_systemes_d_information "delete any e_service content"
./vendor/bin/drush role:perm:add direction_des_systemes_d_information "delete own e_service content"
./vendor/bin/drush role:perm:add direction_des_systemes_d_information "delete e_service revisions"
./vendor/bin/drush role:perm:add direction_des_systemes_d_information "revert e_service revisions"
./vendor/bin/drush role:perm:add direction_des_systemes_d_information "view e_service revisions"
./vendor/bin/drush role:perm:add direction_des_systemes_d_information "translate e_service node"
```

---

## 🏛️ Rôle : `direction_des_affaires_administratives_juridiques_et_generales`

### Permissions de base (backoffice)
```bash
./vendor/bin/drush role:perm:add direction_des_affaires_administratives_juridiques_et_generales "access administration pages,access toolbar,view the administration theme,access content overview,view own unpublished content,view all revisions"
```

### Permissions pour Carrières
```bash
./vendor/bin/drush role:perm:add direction_des_affaires_administratives_juridiques_et_generales "create carrieres content,edit any carrieres content,edit own carrieres content,delete any carrieres content,delete own carrieres content,delete carrieres revisions,revert carrieres revisions,view carrieres revisions,translate carrieres node"
```

### Permissions pour Réglementations
*Note : Ces permissions existaient déjà pour ce rôle*

---

## 📊 Rôle : `direction_de_la_strategie_du_pilotage_et_de_la_coordination_des`

### Permissions de base (backoffice)
```bash
./vendor/bin/drush role:perm:add direction_de_la_strategie_du_pilotage_et_de_la_coordination_des "access administration pages,access toolbar,view the administration theme,access content overview,view own unpublished content,view all revisions"
```

### Permissions pour Organismes sous tutelle
```bash
./vendor/bin/drush role:perm:add direction_de_la_strategie_du_pilotage_et_de_la_coordination_des "edit any organisme_sous_tutelle content,edit own organisme_sous_tutelle content,delete any organisme_sous_tutelle content,delete own organisme_sous_tutelle content,delete organisme_sous_tutelle revisions,revert organisme_sous_tutelle revisions,view organisme_sous_tutelle revisions,translate organisme_sous_tutelle node"
```

### Permissions pour Chiffres clés
*Note : Ces permissions existaient déjà pour ce rôle*

---

## 📢 Rôle : `division_de_la_cooperation_et_de_la_communication_secretariat_ge`

### Permissions de base (backoffice)
```bash
./vendor/bin/drush role:perm:add division_de_la_cooperation_et_de_la_communication_secretariat_ge "access administration pages,access toolbar,view the administration theme,access content overview,view own unpublished content,view all revisions"
```

### Permissions pour Actualités
```bash
./vendor/bin/drush role:perm:add division_de_la_cooperation_et_de_la_communication_secretariat_ge "create actualite content,edit any actualite content,edit own actualite content,delete any actualite content,delete own actualite content,delete actualite revisions,revert actualite revisions,view actualite revisions,translate actualite node"
```

### Permissions pour Agenda
```bash
./vendor/bin/drush role:perm:add division_de_la_cooperation_et_de_la_communication_secretariat_ge "create agenda content,edit any agenda content,edit own agenda content,delete any agenda content,delete own agenda content,delete agenda revisions,revert agenda revisions,view agenda revisions,translate agenda node"
```

### Permissions pour Publications
```bash
./vendor/bin/drush role:perm:add division_de_la_cooperation_et_de_la_communication_secretariat_ge "create publication content,edit any publication content,edit own publication content,delete any publication content,delete own publication content,delete publication revisions,revert publication revisions,view publication revisions,translate publication node"
```

---

## 🔧 Commandes de maintenance

### Vider le cache après chaque modification
```bash
./vendor/bin/drush cache:rebuild
```

### Vérifier les permissions d'un rôle
```bash
./vendor/bin/drush role:list | grep -A 20 "nom_du_role"
```

### Activer le module de filtrage
```bash
./vendor/bin/drush pm:enable mtl_content_filter -y
```

---

## 📝 Notes importantes

1. **Permissions existantes** : Certaines permissions existaient déjà pour certains rôles et n'ont pas été redondamment ajoutées.

2. **Types de contenu** : Les noms des types de contenu utilisés correspondent aux noms réels dans Drupal :
   - `carrieres` (pas `carrieres_professionnelles`)
   - `reglementation` (pas `reglementations`)
   - `actualite` (pas `actualites`)
   - `organisme_sous_tutelle` (pas `etablissement_sous_tutelle`)

3. **Module de filtrage** : Le module `mtl_content_filter` doit être activé pour que le filtrage de la vue content fonctionne.

4. **Ordre d'exécution** : Il est recommandé d'exécuter les commandes dans l'ordre présenté et de vider le cache après chaque série de modifications.

---

## 🚀 Script d'exécution rapide

Pour exécuter toutes les commandes d'un coup, vous pouvez copier-coller les blocs de commandes suivants :

### Bloc 1 : direction_des_systemes_d_information
```bash
./vendor/bin/drush role:perm:add direction_des_systemes_d_information "access administration pages"
./vendor/bin/drush role:perm:add direction_des_systemes_d_information "access toolbar"
./vendor/bin/drush role:perm:add direction_des_systemes_d_information "view the administration theme"
./vendor/bin/drush role:perm:add direction_des_systemes_d_information "access content overview"
./vendor/bin/drush role:perm:add direction_des_systemes_d_information "view own unpublished content"
./vendor/bin/drush role:perm:add direction_des_systemes_d_information "view all revisions"
./vendor/bin/drush role:perm:add direction_des_systemes_d_information "create e_service content"
./vendor/bin/drush role:perm:add direction_des_systemes_d_information "edit any e_service content"
./vendor/bin/drush role:perm:add direction_des_systemes_d_information "edit own e_service content"
./vendor/bin/drush role:perm:add direction_des_systemes_d_information "delete any e_service content"
./vendor/bin/drush role:perm:add direction_des_systemes_d_information "delete own e_service content"
./vendor/bin/drush role:perm:add direction_des_systemes_d_information "delete e_service revisions"
./vendor/bin/drush role:perm:add direction_des_systemes_d_information "revert e_service revisions"
./vendor/bin/drush role:perm:add direction_des_systemes_d_information "view e_service revisions"
./vendor/bin/drush role:perm:add direction_des_systemes_d_information "translate e_service node"
./vendor/bin/drush cache:rebuild
```

### Bloc 2 : direction_des_affaires_administratives_juridiques_et_generales
```bash
./vendor/bin/drush role:perm:add direction_des_affaires_administratives_juridiques_et_generales "access administration pages,access toolbar,view the administration theme,access content overview,view own unpublished content,view all revisions"
./vendor/bin/drush role:perm:add direction_des_affaires_administratives_juridiques_et_generales "create carrieres content,edit any carrieres content,edit own carrieres content,delete any carrieres content,delete own carrieres content,delete carrieres revisions,revert carrieres revisions,view carrieres revisions,translate carrieres node"
./vendor/bin/drush cache:rebuild
```

### Bloc 3 : direction_de_la_strategie_du_pilotage_et_de_la_coordination_des
```bash
./vendor/bin/drush role:perm:add direction_de_la_strategie_du_pilotage_et_de_la_coordination_des "access administration pages,access toolbar,view the administration theme,access content overview,view own unpublished content,view all revisions"
./vendor/bin/drush role:perm:add direction_de_la_strategie_du_pilotage_et_de_la_coordination_des "edit any organisme_sous_tutelle content,edit own organisme_sous_tutelle content,delete any organisme_sous_tutelle content,delete own organisme_sous_tutelle content,delete organisme_sous_tutelle revisions,revert organisme_sous_tutelle revisions,view organisme_sous_tutelle revisions,translate organisme_sous_tutelle node"
./vendor/bin/drush cache:rebuild
```

### Bloc 4 : division_de_la_cooperation_et_de_la_communication_secretariat_ge
```bash
./vendor/bin/drush role:perm:add division_de_la_cooperation_et_de_la_communication_secretariat_ge "access administration pages,access toolbar,view the administration theme,access content overview,view own unpublished content,view all revisions"
./vendor/bin/drush role:perm:add division_de_la_cooperation_et_de_la_communication_secretariat_ge "create actualite content,edit any actualite content,edit own actualite content,delete any actualite content,delete own actualite content,delete actualite revisions,revert actualite revisions,view actualite revisions,translate actualite node"
./vendor/bin/drush role:perm:add division_de_la_cooperation_et_de_la_communication_secretariat_ge "create agenda content,edit any agenda content,edit own agenda content,delete any agenda content,delete own agenda content,delete agenda revisions,revert agenda revisions,view agenda revisions,translate agenda node"
./vendor/bin/drush role:perm:add division_de_la_cooperation_et_de_la_communication_secretariat_ge "create publication content,edit any publication content,edit own publication content,delete any publication content,delete own publication content,delete publication revisions,revert publication revisions,view publication revisions,translate publication node"
./vendor/bin/drush cache:rebuild
```

### Bloc 5 : Activation du module de filtrage
```bash
./vendor/bin/drush pm:enable mtl_content_filter -y
./vendor/bin/drush cache:rebuild
``` 