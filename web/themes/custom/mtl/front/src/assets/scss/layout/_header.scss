$padding-increment: 25px;
.header {
    width: 100%;
    transition: 0.25s all ease-in-out;
    padding: rem(10) 0;
    .toggle & {
        background: $white;
    }
    @media(max-width:993px) {
        .container{
          max-width: 100%;
        }
    }

    &.fixed-top {
        position: absolute;
        @media(max-width:992px) {
            position: fixed;

        }
        .window_scroll & {
            position: fixed;
            padding-bottom: 0;
            box-shadow: 0 10px 12px rgba(67, 101, 151, .05);
        }
    }
    &__top {
        width: 100%;
        display: flex;
        padding-bottom: rem(20);
        &--menu {
            @include margin-end();
            ul {
                display: flex;
                column-gap: rem(50);
                margin-bottom: 0;
                li {
                    a {
                        color: $white;
                        font-size: rem(14);
                        &:after {
                            background-color: $white;
                        }

                        &:hover {
                            color: rgba($white, .8);
                        }
                    }
                }
            }
        }
        &--rsociaux {
            ul {
                display: flex;
                gap: rem(20);
                margin-bottom: 0;
                li {
                    display: flex;
                    flex-wrap: wrap;
                    gap: 30px;
                    a {
                        position: relative;
                        font-size: 0;
                        &:before {
                            font-family: 'icomoon';
                            font-size: rem(18);
                            color: $white;
                            transition: color 200ms ease-in-out 100ms;
                        }
                        &.facebook {
                            &:before {
                                content: "\e905";
                            }
                        }
                        &.instagram {
                            &:before {
                                content: "\e906";
                            }
                        }
                        &.linkedin {
                            &:before {
                                content: "\e907";
                            }
                        }
                        &.youtube {
                            &:before {
                                content: "\e908";
                            }
                        }

                        &:hover {
                            &:before {
                                color: $secondary;
                            }
                        }
                    }
                    
                }
            }
        }

        &--lang {
            // margin-left: rem(40);
            @include start(margin, rem(40));
            a, span {
                color: $white;
            }
            ul {
                color: black;
                height: 100%;
                position: relative;
                align-items: center;
                display: flex;
                padding: 0 25px 0 5px;
                cursor: pointer;
                margin-bottom: 0;
                z-index: 2;
                &::before{
                    content: "\e903";
                   font-family: 'icomoon';
                   position: absolute;
                   @include end(position, 8px);
                   top: 50%;
                   transform: translateY(-50%) rotateZ(0deg);
                   transition: transform .2s ease-in-out;
                   font-size: 6px;
                   color: $white;
                }
                &.list__is-visible{
                   &::before{
                      transform: translateY(-50%) rotateZ(180deg)
                   }
                }
                li{
                    font-size: rem(16);
                    color: $primary;
                        @media(max-width: 1024px){
                            font-size: 14px;
                        }
                    &:first-child{
                        pointer-events: none;
                    }
                    &:nth-child(2) {
                        border-radius: 5px 5px 0 0;
                    }
                    &:last-child {
                        border-radius: 0 0 5px 5px;
                    }
                    &:not(:first-child){
                        position: absolute;
                        top: 100%;
                        width: 100%;
                        // left: 0;
                        @include start(position, 0);
                        background-color: $white;
                        box-shadow: 0 10px 12px rgba(67, 101, 151, .05);
                        display: none;
                        a, span{
                            // padding: 4px rem(25);
                            // padding: 4px 15px 4px 15px;
                            padding-top: 4px;
                            padding-bottom: 4px;
                            @include end(padding, 10px);
                            display: block;
                            transition: all .3s ease;
                            @include end(align, right);
                            color: $primary;
                            &:hover{
                                color: $secondary;
                            }
                        }
    
                    }
                }
                @for $i from 2 through 10 {
                    li:nth-child(#{$i}) {
                        top: calc(100% + (25px * #{$i - 2}));
                        @media(max-width:480px) {
                            top: calc(100% + (30px * #{$i - 2}));  
                        }
                    }
                }
            }
        }
        @media only screen and (max-width: 992px) {
            display: none;
        }
    }

    &__bottom {
        position: relative;
        width: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        .logo {
            margin: 0 auto 15px;
            a {
                display: block;
            }
            .logo-header-sticky {
                display: none;
            }
            img {
                max-width: 100%;
            }
            .toggle & {
                .logo-header-sticky {
                    display: block;
                }
                .logo-header {
                    display: none;
                }
            }
        }
        .wrapper-menu-form {
            position: relative;
            width: 100%;
            display: flex;
            justify-content: center;
            .wrapper {
                display: flex;
                align-items: center;
                padding: 10px 65px 10px 40px;
                background: rgba(#fff, .7);
                border-radius: 22px;
            }
            &::after,  &::before {
                content: "";
                right: -100%;
                top: 0;
                background: #f6f6f6;
                height: 100%;
                position: absolute;
                width: 100%;
                display: none;
            }
            &::after {
                // left: -100%;
                @include start(position, -100%);
            }
            &::before {
                // right: -100%;
                @include end(position, -100%);
            }
        }
        &--mainMenu {
            display: flex;
            nav[role="navigation"] {
                ul {
                    display: flex;
                    margin-bottom: 0;
                    // gap: rem(40);
                    align-items: flex-end;
                    li {
                        & > a {
                            &:after {
                                content: "";
                                background: transparent;
                                left: 0;
                                position: absolute;
                                width: 100%;
                                width: 100%;
                                height: 47px;
                                bottom: -37px;
                            }
                        }
                        a {
                            position: relative;
                            font-size: rem(15);
                            font-family: $rubik-bold;
                            font-weight: bold;
                            color: $black;
                            cursor: pointer;
                            background: transparent;
                            padding: 8px 20px;
                            transition: background 450ms ease;
                            border-radius: rem(17);
                            &:hover {
                                background: $white;
                                color: $primary;
                            }
                          
                        }
                        span.card-title ~ a.click-me {
                            position: absolute;
                        }
                        & > ul {
                            position: absolute;
                            opacity: 0;
                            visibility: hidden;
                            left: 0;
                            width: 100%;
                            top: 48px;
                            transition: width 450ms ease-in;
                            display: grid;
                            grid-template-columns: repeat(4,1fr);
                            background: $lightblue;
                            border-radius: 15px;
                            padding: rem(8);
                            align-items: flex-start;
                            gap: 8px;
                            z-index: 999;
                            &:before {
                                content: "";
                                background-color: transparent;
                                height: rem(20);
                                width: 100vw;
                                position: absolute;
                                top: -13px;
                                // z-index: -1;
                            }
                            & > li {
                                width: 100%;
                                height: 100%;
                                .card {
                                    border: none;
                                    border-radius: 14px;
                                    height: 100%;
                                    .picture {
                                        position: relative;
                                    }
                                    .card-body {
                                        padding: 10px;
                                    }
                                    .card-title {
                                        font-family: $rubik-bold;
                                        font-weight: bold;
                                        font-size: rem(16);
                                        color: $primary;
                                        margin-bottom: 0;
                                        display: block;
                                    }
                                    a {
                                        font-family: $quicksand-regular;
                                        display: block;
                                        color: $gray;
                                        padding: 2px 0;
                                        line-height: 1.3;
                                        margin-left: 0;
                                        &:hover {
                                            background: none;
                                            color: $primary;
                                        }
                                    }
                                }
                            }
                            .window_scroll & {
                                // top: 150px;
                                top: 48px;
                            }
                        }
                        
                        &:nth-of-type(2) {
                            & > ul {
                                zoom: .85;
                                top: 57px;
                            }
                        } 
                        &:nth-of-type(2),&:nth-of-type(3), &:last-of-type {
                            & > ul {
                                grid-template-columns: repeat(3, 1fr);
                            }
                        }
                        &:nth-of-type(3) {
                            & > ul {
                                li {
                                    .card {
                                        .card-body {
                                            padding: 20px 15px;
                                        }
                                    }
                                    
                                    &:hover {
                                        .card-body {
                                            background: $secondary;
                                            border-radius: 14px;
                                            .card-title {
                                                color: $white;
                                            }
                                        }
                                    }
                                }
                            }
                        } 
                        &:nth-of-type(4) {
                            & > ul {
                                grid-template-columns: repeat(2, 1fr);
                                width: 70%;
                                left: 50%;
                                transform: translateX(-50%);
                                li {
                                    .card {
                                        .card-img-top {
                                            max-height: initial;
                                        }
                                    }
                                }
                            }
                        }
                        &.active {
                            a {
                                background: $white;
                                color: $primary;
                            }
                            & > ul {
                                li {
                                    a {
                                        background: transparent;
                                    }
                                }
                            }
                            .window_scroll & {
                                a {
                                    background: $primary;
                                    color: $white;
                                }
                                & > ul {
                                    li {
                                         a {
                                            background: transparent;
                                            color: #707070;
                                            &:hover {
                                                color: $primary;
                                            }
                                        }
                                    }
                                }
                            }
                        }

                        &:hover {
                            & > ul {
                                opacity: 1;
                                visibility: visible;
                                // top: 48px;
                                box-shadow: 0 10px 12px rgba(67, 101, 151, 0.27);
                            }
                        }
                    }
                }
            }
        }
        .disable-menu {
            nav[role="navigation"] {
                pointer-events: none;
                cursor: not-allowed;
            }
        }
        .search-icon {
            width: rem(22);
            height: rem(17);
            position: relative;
            display: inline-block;
            cursor: pointer;
            // margin-left: rem(80);
            @include start(margin, rem(40));
            padding-top: rem(18);

            &::after{
                content: '';
                width: 6px;
                height: 2px;
                background: $black;
                display: inline-block;
                transform: rotate(45deg);
                position: absolute;
                top: 15px;
                left: 15px;
                border-radius: 0 40px 40px 0;
            }
            &::before{
                content: '';
                width: 17px;
                height: 17px;
                border: 2.5px solid $black;
                display: inline-block;
                position: absolute;
                top: 0;
                left: 0;
                border-radius: 50%;
                transition: top 0.3s ease-in 0s, left 0.3s ease-in 0s, border-radius 0.3s ease-in 0s, border-width 0.3s ease-in 0s, width 0.3s ease-in 0s, height 0.3s ease-in 0s, background-color 0.1s ease-in 0s;
        
            }
            &.search-icon__active{
                &::after{
                    width: 24px;
                    left: 0;
                    top: 10px;
                    transition: all 0.3s ease-out 0s;
                    border-radius: 40px;
                }
                &::before{
                    border-width: 0;
                    top: 12px;
                    left: 12px;
                    transition: top 0.3s ease-in 0s, left 0.3s ease-in 0s, border-radius 0s ease-in 0.3s, border-width 0.3s ease-in 0s, width 0.3s ease-in 0s, height 0.3s ease-in 0s, background-color 0s ease-in 0.3s;
                    width: 24px;
                    height: 2px;
                    top: 10px;
                    left: 0;
                    transform: rotate(135deg);
                    background-color: $black;
                    border-radius: 40px;
                }
            }
        }

        &--overlay {
            // display: none;
            // position: relative;
            // max-width: rem(1000);
            // padding-top: 5px;

            position: absolute;
            /* opacity: 1; */
            /* visibility: visible; */
            width: 60%;
            width: 774px;
            height: 170px;
            top: 142px;
            transition: width 450ms ease-in;
            display: none;
            background: rgb(241, 247, 255);
            border-radius: 15px;
            z-index: 999;
            transform: translateX(-50%);
            left: 50%;

            & > form{
                background: #f7f7f7;
                background: rgba(#fff, .7);
                background: transparent;
                padding: 0;
                border-radius: 0;
                box-shadow: none;
                .form-item{
                    // width: 100%;
                    // position: relative;
                    // margin: 0;

                    width: 90%;
                    position: absolute;
                    /* margin: 0; */
                    top: 50%;
                    transform: translate(-50%, -50%);
                    left: 50%;
                    &::after {
                        content: "\e909";
                        font-family: 'icomoon';
                        font-size: rem(18);
                        color: $black;
                        position: absolute;
                        @include end(position, 35px);
                        top: 54%;
                        transform: translateY(-50%);

                    }
                    & > label{
                        display: none;
                    }
                    input[type="search"],input[type="text"] {
                        font-family: $quicksand-regular;
                        width: 100%;
                        outline: none;
                        border: none;
                        padding: 10px 64px 10px 20px;
                        font-size: rem(16);
                        color: $black;
                        background-color: rgba(#fff, .7);
                        border-radius: rem(24);
                        box-shadow: 0 10px 12px rgba(67, 101, 151, .05);
                        &::placeholder{
                            color: #b0b0b0;
                        }
                    }
                }
                .form-actions{
                    position: absolute;
                    // right: rem(20);
                    @include end(position, rem(20));
                    width: 30px;
                    height: 30px;
                    top: 50%;
                    transform: translateY(-50%);
                    input[type="submit"] {
                        cursor: pointer;
                        background: none;
                        border: none;
                        font-size: 0;
                        width: 30px;
                        height: 30px;
                        right: 32px;
                        position: absolute;
                    }
                }
            }
        }
    }
    .window_scroll & {
        // background-color: $white;
        background: $white;
        // box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.11);
        transition: .25s background-color ease-in-out, .25s height ease-in-out;
        &__top {
            display: none;
        }
        &__bottom {
            padding-bottom: 0;
            border-bottom: none;
            align-items: center;
            .logo {
                .logo-header {
                    display: none;
                }
                .logo-header-sticky {
                    display: block;
                }
            }
            .wrapper-menu-form {
                background: #f6f6f6;
                &::after,  &::before {
                    display: block;
                }
                .wrapper {
                    background: transparent;
                }
            }
            &--mainMenu {
                nav {
                    ul, ul.navbar-nav {
                        height: auto;
                        li {
                            a {
                                // color: $primary;
                                &:hover {
                                    background: $primary;
                                    color: $white;
                                }
    
                            }

                            & > ul {
                                top: rem(87);
                                &:before {
                                    height: 28px;
                                    width: 84%;
                                    top: -16px;
                                    right: 40px;
                                }
                            }
                        }
                    }
                }
            }
            .search-icon {
                align-self: auto;
                padding-top: 0;
            }
            &--overlay {
                position: absolute;
                bottom: -45px;
                & > form {
                    .form-item {
                        input[type="text"] {
                            background-color: #f6f6f6;
                        }
                    }
                }
            }
        }
    }
    @media only screen and (max-width: 1024px) {
        &__bottom {
            .wrapper-menu-form {
                width: 100%;
            }
            &--mainMenu {
                nav[role="navigation"] {
                    ul {
                        gap: rem(10);
                    }
                }
            }
            .search-icon {
                margin-left: 20px;
            }

            &--overlay {
                // width: 100%;
                // top: 100px;
            }
        }
    }
    @media only screen and (max-width: 992px) {
        min-height: 100px;
        padding: 0;
        &__bottom {
            flex-direction: inherit;
            .logo {
                // margin: 0;
                left: 50%;
                // @include start(position, 50%);
                position: relative;
                transform: translateX(-50%);
                margin-bottom: 0;
                img {
                    zoom: 1.3;
                    max-width: max-content;
                }
            }
            .wrapper-menu-form {
                .wrapper {
                    background: transparent;
                    width: auto;
                    padding: 0;
                }
                .window_scroll & {
                    background: transparent;
                    &::after, &::before {
                        display: none;
                    }
                }
            }
            &--mainMenu {
                nav[role="navigation"] {
                    ul {
                        flex-direction: column;
                        align-items: flex-start;
                        gap: rem(12);
                        li {
                            width: 100%;
                            padding: 15px;
                            & > a {
                                &:after {
                                    display: none;
                                }
                            }
                            a {
                                padding: 0;
                                border-radius: 0;
                                transition: none;
                                color: $white;
                                font-size: 18px;
                                &:hover {
                                    background: transparent;
                                    color: $white;
                                }
    
                            }
                            & > ul {

                                opacity: 1;
                                visibility: visible;
                                position: relative;
                                top: 0;
                                padding-top: 14px;
                                flex-direction: column;
                                width: 100%;
                                padding: 15px;
                                background: transparent;
                                
                                .window_scroll & {
                                    top: 0;
                                }
                                &:before {
                                    display: none;
                                }
                                & > li {

                                    display: block;
                                    background: none;
                                    padding: 0;
                                    gap: 0;
                                    border-radius: none;
                                    
                                    .card {
                                        position: relative;
                                        background: none;
                                        display: block;
                                        min-width: 100%;
                                        padding: 5px 0;
                                        span.img-overlay {
                                            display: none;
                                        }
                                        img {
                                            display: none;
                                        }
                                        .card-body {
                                            padding: 0;
                                            // pointer-events: none;
                                            & > div {
                                                display: none;
                                                padding: 5px 0;
                                            }
                                        }
                                        .card-title {
                                            position: relative;
                                            color: $white;
                                            margin-bottom: 0;

                                            &:after {
                                                content: "\e903";
                                                font-family: 'icomoon';
                                                position: absolute;
                                                // right: 0.9375rem;
                                                @include end(position, 0.9375rem);
                                                top: 0.625rem;
                                                font-size: 0.5rem;
                                                color: $white;
                                                top: 50%;
                                                transform: translateY(-50%) rotate(0);
                                                transition: transform 350ms ease;
                                            }
                                            &.open {
                                                &:after {
                                                    transform: translateY(-50%) rotate(180deg);
                                                }
                                            }
                                        }
    
                                        a {
                                            color: $white;
                                            padding-left: 0;
                                            font-size: 16px;
                                            // pointer-events: none;
                                            &:hover {
                                                background: none;
                                                color: $white;
                                            }
                                        }
                                    }
                                }
                            }
                            &:nth-of-type(3) {
                                & > ul {
                                    li {
                                        .card {
                                            .card-body {
                                                padding: 0;
                                            }
                                        }
                                        &.active {
                                            ul {
                                                li {
                                                    a {
                                                        padding-left: 0;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            } 
                            &:nth-of-type(4) {
                                & > ul {
                                    width: 100%;
                                    left: 0;
                                    transform: none;
                                }
                            }
                            .card-body {
                                &.card-body--toggle {
                                    span.card-title {
                                        &::after {
                                            display: none;
                                        }
                                    }
                                }
                            }
                            &.active {
                                a {
                                    background: transparent !important;
                                    color: $white;
                                }
                                ul {
                                    li {
                                        a {
                                            color: $white;
                                            padding-left: 20px;
                                            .window_scroll & {
                                                color: white;
                                            }
                                        }
                                    }
                                }
                            }
    
                            &:hover {
                                & > ul {
                                    opacity: 1;
                                    visibility: visible;
                                    top: auto;
                                    .window_scroll & {
                                        top: 0;
                                    }
                                }
                              
                            }

                        }
                    }
                    & > ul {
                        @include header-footer-mobile;
                        &>li {
                            padding: 0;
                            &:not(:last-child) {
                                margin-bottom: rem(10);
                            }
                            & > a {
                                &.open {
                                    &:before {
                                        transform: translateY(-50%) rotate(180deg);
                                    }
                                }
                            }
                        }
                    }
                    ul.langue-switcher {
                        li {
                            padding: 2px 8px;
                            &:first-child {
                                padding-top: 14px;
                            }
                            &:not(:first-child) {
                                background-color: $secondary;
                            }
                            a {
                                font-size: 15px;
                                color: $white;
                            }
                        }
                        &:before {
                            color: $white;
                            font-size: 0.5rem;
                            top: 56%;
                        }
                    }
                }
            }
            .search-icon {
                position: absolute;
                top: 0;
                zoom: 1.4;
                right: 50px;
                margin-right: 0;
                &::after {
                    background: $white;
                }
                &::before {
                    border: 2.5px solid $white;
                }
                .window_scroll &, body.toggle & {
                    top: 4px;
                    &::after {
                        background: $primary;
                    }
                    &::before {
                        border: 2.5px solid $primary;
                    }
                }
                display: none;
            }
            &--overlay {
                position: absolute;
                top: 90px;
                & > form {
                    .form-item {
                        input[type="search"],input[type="text"] {
                            background-color: rgba(255, 255, 255, 0.2);
                            color: $black;
                            border-radius: 14px;
                            margin: 0;
                            padding-right: 82px;
                            &::placeholder{
                                color: $white;
                            }
                        }
                       
                        &:after {
                            color: $white;
                        }
                        .window_scroll & {
                            input[type="search"],input[type="text"] {
                                background-color: $white;
                            } 
                        }
                    }
                }
            }
        }
    }

    @media only screen and (max-width: 480px) {
        min-height: 80px;
        &__top {
            &--lang {
                ul {
                    margin-bottom: 10px !important;
                    margin-left: auto;
                    width: max-content;
                    &:before {
                        top: 64%;
                    }
                }
            }
        }
        &__bottom {
            .logo {
                img {
                    zoom: .9;
                    // max-width: 100%;
                    // transform: scale(1.2);
                }
            }
            &--mainMenu {
                nav[role="navigation"] {
                    ul.langue-switcher {
                        // &:before {
                        //     left: ;
                        // }
                        li {
                            padding: 10px 18px 10px 10px;
                            &:first-child {
                                padding-top: 8px;
                                padding-right: 5px;
                                padding-bottom: 5px;
                            }
                        }
                    }
                }
            }
            
            .search-icon {
                zoom: 1.1;
                top: 0;
                margin-right: 10px;
                .window_scroll & {
                    top: 4px;
                }
            }
        }
    }
    @media only screen and (min-width: 993px) {
        .card {
            .card-body {
                & >.card-link {
                    pointer-events: none;
                }
            }
        }
    }
}

// Menu Burger
.header__bottom--mainMenu {
    .nav-toggle-label {
        position: absolute;
        // right: 0;
        @include end(position, 0);
        z-index: 101;
        display: none;
        span {
            display: block;
            background: $white;
            height: rem(6);
            width: rem(46);
            position: relative;
            transition: all ease-in-out 500ms;

            &::before,
            &::after {
                content: "";
                position: absolute;
                background: $white;
                height: rem(6);
                transition: all ease-in-out 500ms;
            }

            &::before {
                width: rem(46);
                transform: translateY(24px);
                right: 0;
            }

            &::after {
                width: rem(46);
                transform: translateY(12px);
            }
        }
        .window_scroll &, body.toggle & {
            span {
                background: $primary;
                &::before,
                &::after {
                    background: $primary;
                }
            }
        }
    }

    .nav-toggle {
        display: none;
    }

    // Animation au clic sur le burger
    .nav-toggle:checked ~ .nav-toggle-label {
        span {
            transform: translateY(12px) rotate(45deg);

            &::before {
                opacity: 0;
            }

            &::after {
                transform: translateY(0) rotate(90deg);
            }
        }
    }
    @media(max-width:992px) {
        .nav-toggle-label {
            display: block;
            top: 0;
            cursor: pointer;
            height: 30px;
        }
        .nav-toggle ~ nav[role="navigation"] {
                transform: translateX(100%);
                background: $primary;
                position: fixed;
                width: 100vw;
                height: 100vh;
                right: 0;
                top: 100px;
                overflow-y: scroll;
                padding: 30px 30px 130px ;
                transition: transform 400ms ease-in;
        }
        .nav-toggle:checked ~ nav[role="navigation"] {
            transform: translateX(0);
            left: 0;
        }
    }
    @include media-max(480px) {
        .nav-toggle-label {
            zoom: .7;
            top: 0;
        }
        .nav-toggle ~ nav[role="navigation"] {
            // top: 95px;
            top: 80px;
        }
    }
}
html.toggle {
    overflow-y: hidden;
}

//Header Mobile
.header-top-mobile {
    display: flex;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 14px;
    margin-bottom: 10px;
    padding: 4px 8px;
    .language-switcher-language-url {
        order: 2;
    }
    .header__bottom--overlay {
        order: 1;
        display: block;
        top: 0;
        position: relative;
        padding: 0;

        height: auto;
        background: transparent;
        transform: none;
        left: auto;
        width: 100%;
    }
    .header__bottom--overlay > form .form-item {
        width: 100%;
        position: relative;
        margin: 0;
        transform: none;
        top: auto;
        left: auto;
    }

    @media(max-width:480px) {
        flex-direction: column;
        padding: 0;
        background-color: transparent;
        .language-switcher-language-url {
            order: 1;
        }
        .header__bottom--overlay {
            margin-bottom: 10px;
        }
    }
}
.header-bottom-mobile {
   .header__top--menu  {
    ul.menu {
        display: flex;
        flex-direction: row;
        background-color: rgba(255, 255, 255, 0.1);
        border-radius: 14px;
        padding: 20px;
        margin-top: 10px;
        flex-wrap: wrap;
        li {
            padding: 0;
            width: auto;
            &:not(:last-child) {
                margin-right: 10px;
            }
            a {
                font-size: 15px;
            }
        }
        @media(max-width:992px) {
            flex-direction: column;
        }
    }
   } 
   .header__top--rsociaux {
        background-color: rgba(255, 255, 255, 0.1);
        border-radius: 14px;
        padding: 20px;
        margin-top: 10px;
        ul {
            flex-direction: row !important;
            li {
                padding: 2px !important;
                width: auto !important;
            }
        }
   }
}

@keyframes blink {
    0%, 100% { background-color: #fff; }
    50% { background-color: #ffe680; } /* jaune clair */
  }
  
  .blink-on-focus {
    animation: blink 0.4s ease-in-out 2; /* 2 clignotements */
  }