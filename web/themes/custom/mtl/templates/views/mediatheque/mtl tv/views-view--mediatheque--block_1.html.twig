{#
/**
 * @file
 * Default theme implementation for main view template.
 *
 * Available variables:
 * - attributes: Remaining HTML attributes for the element.
 * - css_name: A CSS-safe version of the view name.
 * - css_class: The user-specified classes names, if any.
 * - header: The optional header.
 * - footer: The optional footer.
 * - rows: The results of the view query, if any.
 * - empty: The content to display if there are no rows.
 * - pager: The optional pager next/prev links to display.
 * - exposed: Exposed widget form/info to display.
 * - feed_icons: Optional feed icons to display.
 * - more: An optional link to the next page of results.
 * - title: Title of the view, only used when displaying in the admin preview.
 * - title_prefix: Additional output populated by modules, intended to be
 *   displayed in front of the view title.
 * - title_suffix: Additional output populated by modules, intended to be
 *   displayed after the view title.
 * - attachment_before: An optional attachment view to be displayed before the
 *   view content.
 * - attachment_after: An optional attachment view to be displayed after the
 *   view content.
 * - dom_id: Unique id for every view being printed to give unique class for
 *   JavaScript.
 *
 * @see template_preprocess_views_view()
 *
 * @ingroup themeable
 */
#}
{%
    set classes = [
      dom_id ? 'js-view-dom-id-' ~ dom_id,
    ]
  %}
  <section class="bloc-video elements-wrapper digit">
    <div class="container">
  <div{{ attributes.addClass(classes) }}>
    <div class="mtl-tv">
        <h2 class="h2-title text-center">
            {{ view.getTitle()}}
        </h2>
    {{ title_prefix }}
    {{ title }}
    {{ title_suffix }}
  
    {% if header %}
      <header>
        {{ header }}
      </header>
    {% endif %}
  
    {{ exposed }}
    {{ attachment_before }}
    <div class="swiper swiper-video">
        <div class="swiper-wrapper mtl-tv__items">
            {% if rows -%}
            {{ rows }}
            {% elseif empty -%}
            {{ empty }}
            {% endif %}
        </div>
        <div class="swiper-pagination blue"></div>
    </div>
    {{ pager }}
  
    {{ attachment_after }}
    {% set more_link = more|render|striptags|trim %} 

        <div class="wraper-btn d-flex justify-content-center">
          <a class="btn btn-success bold" href="{{more_url}}" >
            {{more_link}}
              <i class="fa-solid fa-arrow-right"></i>
          </a>
      </div>    
  
    {% if footer %}
      <footer>
        {{ footer }}
      </footer>
    {% endif %}
  
    {{ feed_icons }}
  </div>
</div>
</div>
<div class="video-modal">
        <div id="video-modal-content" class="video-modal-content">
            <iframe id="youtube" width="100%" height="100%" frameborder="0" allow="autoplay" allowfullscreen src=""></iframe>
            <span href="#" class="close-video-modal"> X </span>
    
        </div>
        <div class="overlay">

        </div>
    </div>
</section>